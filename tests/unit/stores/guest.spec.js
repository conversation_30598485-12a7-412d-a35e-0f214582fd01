import store from "@/store";

describe("mergeGuestScanData Mutation", () => {
  let state;
  let scanData;
  beforeEach(() => {
    state = {
      list: [
        {
          selected: true,
          name: "<PERSON>",
          surname: "<PERSON><PERSON>",
          email: "<EMAIL>",
          telephone: "1234567890",
          address: { street: "Old Street", city: "Old City" },
        },
      ],
    };

    scanData = {
      name: "<PERSON>",
      surname: "<PERSON>",
      gender: "male",
      birthday_date: "2024-01-01",
      document_type: "passport",
      document_number: "PAQ122463",
      address: { street: "New Street", city: "New City" },
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it("should update guest data with scanned data", () => {
    store.state.guest = state;
    const data = scanData;
    const holderNotModifiable = false;
    store.commit("guest/mergeGuestScanData", { data, holderNotModifiable });

    expect(store.state.guest.list[0]).toMatchObject({
      selected: true,
      name: "<PERSON>", // Updated
      surname: "<PERSON>", // Updated
      full_name: "<PERSON>", // Updated
      email: "<EMAIL>", // Preserved
      telephone: "1234567890", // Preserved
      address: { street: "New Street", city: "New City" }, // Updated
      gender: "male", // Updated
      birthday_date: "2024-01-01", // Updated
      document_type: "passport", // Updated
      document_number: "PAQ122463", // Updated
    });
  });

  it("should preserve holder data if holderNotModifiable is true and is holder", () => {
    state.list[0].holder = true;
    store.state.guest = state;
    const data = scanData;
    const holderNotModifiable = true;
    store.commit("guest/mergeGuestScanData", { data, holderNotModifiable });

    expect(store.state.guest.list[0]).toMatchObject({
      selected: true,
      name: "John", // Preserved
      surname: "Doe", // Preserved
      email: "<EMAIL>", // Preserved
      telephone: "1234567890", // Preserved
      address: { street: "New Street", city: "New City" }, // Updated
      gender: "male", // Updated
      birthday_date: "2024-01-01", // Updated
      document_type: "passport", // Updated
      document_number: "PAQ122463", // Updated
    });
  });

  it("should update holder data if holderNotModifiable is true and is not holder", () => {
    state.list[0].holder = false;
    store.state.guest = state;
    const data = scanData;
    const holderNotModifiable = true;
    store.commit("guest/mergeGuestScanData", { data, holderNotModifiable });

    expect(store.state.guest.list[0]).toMatchObject({
      selected: true,
      name: "Jane", // Updated
      surname: "Smith", // Updated
      email: "<EMAIL>", // Preserved
      telephone: "1234567890", // Preserved
      address: { street: "New Street", city: "New City" }, // Updated
      gender: "male", // Updated
      birthday_date: "2024-01-01", // Updated
      document_type: "passport", // Updated
      document_number: "PAQ122463", // Updated
    });
  });

  it("should clear fields if scanned data is null or empty", () => {
    store.state.guest = state;
    const data = {
      name: null,
      surname: "",
      email: null,
      telephone: "",
      address: null,
    };
    const holderNotModifiable = false;
    store.commit("guest/mergeGuestScanData", { data, holderNotModifiable });

    expect(store.state.guest.list[0]).toMatchObject({
      selected: true,
      name: "", // Cleared
      surname: "", // Cleared
      email: "<EMAIL>", // Preserved
      telephone: "1234567890", // Preserved
      address: { street: null, city: null }, // Updated
    });
  });
});

describe("updateDocumentsEmail Mutation", () => {
  let state;

  beforeEach(() => {
    state = {
      list: [
        {
          selected: true,
          name: "John",
          surname: "Doe",
          email: "<EMAIL>",
          documentsEmail: null,
        },
        {
          selected: false,
          name: "Jane",
          surname: "Smith",
          email: "<EMAIL>",
          documentsEmail: null,
        },
      ],
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it("should update documentsEmail for selected guest only", () => {
    store.state.guest = state;
    const documentsEmail = "<EMAIL>";

    store.commit("guest/updateDocumentsEmail", documentsEmail);

    expect(store.state.guest.list[0]).toMatchObject({
      selected: true,
      name: "John",
      surname: "Doe",
      email: "<EMAIL>", // Should remain unchanged
      documentsEmail: "<EMAIL>", // Should be updated
    });

    // Non-selected guest should remain unchanged
    expect(store.state.guest.list[1]).toMatchObject({
      selected: false,
      name: "Jane",
      surname: "Smith",
      email: "<EMAIL>",
      documentsEmail: null, // Should remain unchanged
    });
  });

  it("should not affect main email when updating documentsEmail", () => {
    store.state.guest = state;
    const originalEmail = state.list[0].email;
    const documentsEmail = "<EMAIL>";

    store.commit("guest/updateDocumentsEmail", documentsEmail);

    expect(store.state.guest.list[0].email).toBe(originalEmail);
    expect(store.state.guest.list[0].documentsEmail).toBe(documentsEmail);
    expect(store.state.guest.list[0].email).not.toBe(
      store.state.guest.list[0].documentsEmail
    );
  });

  it("should handle null documentsEmail", () => {
    store.state.guest = state;

    store.commit("guest/updateDocumentsEmail", null);

    expect(store.state.guest.list[0].documentsEmail).toBeNull();
    expect(store.state.guest.list[0].email).toBe("<EMAIL>");
  });

  it("should handle empty string documentsEmail", () => {
    store.state.guest = state;

    store.commit("guest/updateDocumentsEmail", "");

    expect(store.state.guest.list[0].documentsEmail).toBeNull(); // Guest constructor converts "" to null
    expect(store.state.guest.list[0].email).toBe("<EMAIL>");
  });
});

describe("UPDATE_DOCUMENTS_EMAIL Action", () => {
  let state;

  beforeEach(() => {
    state = {
      list: [
        {
          selected: true,
          name: "John",
          surname: "Doe",
          email: "<EMAIL>",
          documentsEmail: null,
        },
      ],
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it("should dispatch updateDocumentsEmail mutation", async () => {
    store.state.guest = state;
    const documentsEmail = "<EMAIL>";

    await store.dispatch("guest/UPDATE_DOCUMENTS_EMAIL", documentsEmail);

    expect(store.state.guest.list[0].documentsEmail).toBe(documentsEmail);
    expect(store.state.guest.list[0].email).toBe("<EMAIL>");
  });
});

describe("getChildGuests Getter", () => {
  let state;

  beforeEach(() => {
    state = {
      list: [
        { pax_type: "AD", name: "Adult" },
        { pax_type: "CH", name: "Child" },
        { pax_type: "JR", name: "Junior" },
        { pax_type: "BB", name: "Baby" },
      ],
    };
  });

  it("should return only child guests (CH, JR, BB)", () => {
    store.state.guest = state;

    const childGuests = store.getters["guest/getChildGuests"];

    expect(childGuests).toHaveLength(3);
    expect(childGuests.map((g) => g.pax_type)).toEqual(["CH", "JR", "BB"]);
    expect(childGuests.map((g) => g.name)).toEqual(["Child", "Junior", "Baby"]);
  });

  it("should return empty array when no child guests", () => {
    state.list = [
      { pax_type: "AD", name: "Adult1" },
      { pax_type: "AD", name: "Adult2" },
    ];
    store.state.guest = state;

    const childGuests = store.getters["guest/getChildGuests"];

    expect(childGuests).toHaveLength(0);
  });
});
