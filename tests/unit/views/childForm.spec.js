import Vuex from "vuex";
import Vuelidate from "vuelidate";
import { mount, createLocalVue } from "@vue/test-utils";
import ChildForm from "@/views/ChildForm";
import brandModule from "@/store/brand";
import loading from "@/store/loading";
import reservationsModule from "@/store/reservations";
import modal from "@/store/modal";
import guestModule from "@/store/guest";
import { i18n } from "@/locales";
import { countries } from "@/utils/countries";
import childsManagement from "@/mixins/childsManagement";
import { format } from "date-fns";
import { DEFAULT_FORMAT, EMITTED_VALUE } from "@/mixins/dateFormat.js";
import { nextTick } from "vue";

const localVue = createLocalVue();

// need to import vuex
localVue.use(Vuex);
// if the components mounted reference $v we need to import Vuelidate
localVue.use(Vuelidate);

// Get birth date according to the argument passed
function getBirthDate(yearsOld) {
	const today = new Date();
	let birthDate = new Date(today.setFullYear(today.getFullYear() - yearsOld)); // Substract years from today's date
	birthDate = format(birthDate, DEFAULT_FORMAT);
	return birthDate;
}

function getBirthDateFormatted(yearsOld) {
	const today = new Date();
	let birthDate = new Date(today.setFullYear(today.getFullYear() - yearsOld)); // Substract years from today's date
	birthDate = format(birthDate, EMITTED_VALUE);
	return birthDate;
}

describe("Child Form", () => {
	let store;
	let wrapper;
	let mockRoute;
	let mockRouter;
	let brand;
	let guest;
	let reservations;

	beforeEach(async () => {
		mockRouter = {
			push: jest.fn(() => Promise.resolve()),
		};
		reservations = {
			...reservationsModule,
			state: {
				childAgeAttempts: 0,
				reservationSelected: {
					res_localizer: "234kj",
				},
			},
		};
		guest = {
			...guestModule,
			getters: {
				getSelectedGuest: () => {
					return {
						pax_type: "CH",
					};
				},
			},
		};
		brand = {
			...brandModule,
			state: {
				brandId: 1,
				mainColor: "123",
				country: "ES",
				config: {
					identification: {
						child_form: [
							[
								{
									active: "true",
									required: "true",
									maxLength: "50",
									minLength: "2",
									name: "name",
									type: "text",
								},
								{
									active: "true",
									required: "true",
									maxLength: "50",
									minLength: "2",
									name: "surname",
									type: "text",
								},
								{
									active: "true",
									required: "true",
									name: "nationality",
									type: "autocomplete",
									options: countries,
								},
								{
									active: "true",
									required: "true",
									name: "birthday",
									type: "date",
								},
								{
									active: "false",
									required: "false",
									name: "kinship",
									type: "select",
								},
								{
									active: "false",
									required: "false",
									name: "postal_code",
									type: "text",
								},
								{
									active: "true",
									fill_from_holder: "true",
									required: "false",
									name: "address",
									type: "autocomplete",
								},
								{
									active: "true",
									fill_from_holder: "true",
									required: "false",
									name: "municipality",
									type: "text",
								},
								{
									active: "true",
									fill_from_holder: "true",
									required: "false",
									name: "telephone",
									type: "phone",
								},	
								{
									active: "true",
									fill_from_holder: "true",
									required: "false",
									name: "email",
									type: "email",
								},									
							],
						],
					},
					child_required_identity_documents_age: 14,
					max_attempts_child: 20,
				},
			},
		};
		store = () => {
			return new Vuex.Store({
				modules: {
					reservations,
					modal,
					loading,
					brand,
					guest,
				},
			});
		};
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it("All inputs enabled", async () => {
		wrapper = await mount(ChildForm, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		expect(wrapper.find('[data-test="child-name-input"]').isVisible()).toBe(
			true,
		);
		expect(wrapper.find('[data-test="child-surname-input"]').isVisible()).toBe(
			true,
		);
		expect(wrapper.find('[data-test="child-birthday-input"]').isVisible()).toBe(
			true,
		);

		expect(
			wrapper.find('[data-test="child-nationality-input"]').isVisible(),
		).toBe(true);

		expect(wrapper.find('[data-test="child-data-submit"]').isVisible()).toBe(
			true,
		);

		wrapper.destroy();
	});

	it("Renders only fields marked as active", async () => {
		brand.state.config.identification.child_form = [
			[
				{
					active: "false",
					required: "false",
					maxLength: "50",
					minLength: "2",
					name: "name",
					type: "text",
				},
				{
					active: "false",
					required: "false",
					maxLength: "50",
					minLength: "2",
					name: "surname",
					type: "text",
				},
				{
					active: "false",
					required: "false",
					name: "nationality",
					type: "autocomplete",
					options: countries,
				},
				{
					active: "true",
					required: "true",
					name: "birthday",
					type: "date",
				},
				{
					active: "true",
					required: "false",
					name: "postal_code",
					type: "text",
				},
				{
					active: "true",
					fill_from_holder: "true",
					required: "false",
					name: "address",
					type: "autocomplete",
				},
				{
					active: "true",
					fill_from_holder: "true",
					required: "false",
					name: "municipality",
					type: "text",
				},
				{
					active: "true",
					fill_from_holder: "true",
					required: "false",
					name: "telephone",
					type: "phone",
				},	
				{
					active: "true",
					fill_from_holder: "true",
					required: "false",
					name: "email",
					type: "email",
				},
			],
		];

		wrapper = await mount(ChildForm, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		expect(wrapper.find('[data-test="child-name-input"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="child-surname-input"]').exists()).toBe(false);
		expect(wrapper.find('[data-test="child-nationality-input"]').exists()).toBe(false);

		
		expect(wrapper.find('[data-test="child-birthday-input"]').isVisible()).toBe(true);
		expect(wrapper.find('[data-test="child-address-input"]').isVisible()).toBe(true);
		expect(wrapper.find('[data-test="child-municipality-input"]').isVisible()).toBe(true);
		expect(wrapper.find('[data-test="child-telephone-input"]').isVisible()).toBe(true);
		expect(wrapper.find('[data-test="child-email-input"]').isVisible()).toBe(true);
		expect(wrapper.find('[data-test="child-postal_code-input"]').isVisible()).toBe(true);
		expect(wrapper.find('[data-test="child-data-submit"]').isVisible()).toBe(true);

		wrapper.destroy();
	});

	it("Continue Button is disabled if required inputs are not filled", async () => {
		wrapper = await mount(ChildForm, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		expect(
			wrapper.find('[data-test="child-data-submit"]').attributes("disabled"),
		).toBe("disabled");

		await wrapper.find('[data-test="child-name-input"]').setValue("Name");
		expect(
			wrapper.find('[data-test="child-data-submit"]').attributes("disabled"),
		).toBe("disabled");

		await wrapper.find('[data-test="child-surname-input"]').setValue("Surname");
		expect(
			wrapper.find('[data-test="child-data-submit"]').attributes("disabled"),
		).toBe("disabled");

		await wrapper.find('[data-test="child-nationality-input"]').setValue("Sp");
		await wrapper
			.find('[data-test="child-nationality-input-option"]')
			.trigger("click");

		expect(
			wrapper.find('[data-test="child-data-submit"]').attributes("disabled"),
		).toBe("disabled");

		await wrapper.find("#child-birthday-input").setValue("01-01-2000");

		expect(
			wrapper.find('[data-test="child-data-submit"]').attributes("disabled"),
		).toBe(undefined);

		wrapper.destroy();
	});

	it("If future date is set, show error message", async () => {
		wrapper = await mount(ChildForm, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await wrapper.find("#child-birthday-input").setValue(getBirthDate(-1)); // one year in the future;

		expect(wrapper.find('[data-test="birthday_date_error"]').isVisible()).toBe(
			true,
		);
		wrapper.destroy();
	});

	it("Data is saved correctly if requirements are filled", async () => {
		wrapper = await mount(ChildForm, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");

		await wrapper.find('[data-test="child-name-input"]').setValue("Name");
		await wrapper.find('[data-test="child-surname-input"]').setValue("Surname");
		await wrapper.find('[data-test="child-nationality-input"]').setValue("Sp");
		await wrapper.find('[data-test="child-nationality-input"]').trigger("input");
		await wrapper.find('[data-test="child-nationality-input-option"]').trigger("click");
		await wrapper.find("#child-birthday-input").setValue(getBirthDate(13));
		await wrapper.find('[data-test="child-email-input"]').setValue("<EMAIL>");
		
		const telephoneInput = wrapper.find('[data-test="child-telephone-input"]');
		await telephoneInput.vm.$emit('inputChanged', {
			value: {
				value: "1163335656",
				dialCode: "+54",
				countryCode: "ARG"
			},
			error: false
		});

		await nextTick();

		wrapper.vm.telephone = {
			value: "1163335656",
			dialCode: "+54",
			countryCode: "ARG"
		};

		await wrapper.vm.validateChildData();

		expect(storeDispatch).toBeCalledWith("guest/UPDATE_GUEST", {
			pax_type: "CH",
			birthday_date: getBirthDateFormatted(13),
			nationality: "ESP",
			name: "Name",
			surname: "Surname",
			email: "<EMAIL>",
			telephone: {
				value: "1163335656",
				dialCode: "+54",
				countryCode: "ARG"
			},
			address: {
				CCAA: "",
				city: "",
				country: "",
				postal_code: "",
				province: "",
				region: "",
				street: "",
				street_number: "",
				subregion: "",
			}
		});

		wrapper.destroy();
	});

	it("User is redirected to Confirmation view if data was valid", async () => {
		mockRoute = { name: "Confirmation" };

		wrapper = await mount(ChildForm, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");

		await wrapper.find('[data-test="child-name-input"]').setValue("Name");
		await wrapper.find('[data-test="child-surname-input"]').setValue("Surname");
		await wrapper.find('[data-test="child-nationality-input"]').setValue("Sp");
		await wrapper
			.find('[data-test="child-nationality-input-option"]')
			.trigger("click");
		await wrapper.find("#child-birthday-input").setValue(getBirthDate(13));
		await wrapper.vm.validateChildData();

		expect(storeDispatch).not.toBeCalledWith("modal/VISIBLE", true);
		expect(wrapper.vm.$router.push).toHaveBeenCalledWith({
			name: "Confirmation",
		});

		wrapper.destroy();
	});

	it("Modal pops up if birth date doesn't correspond to a child", async () => {
		wrapper = await mount(ChildForm, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		await wrapper.find("#child-birthday-input").setValue(getBirthDate(15));
		await wrapper.vm.validateChildData();

		expect(storeDispatch).toBeCalledWith("modal/VISIBLE", true);
		expect(wrapper.find('[data-test="modalError"]').isVisible()).toBe(true);
		expect(wrapper.find('[data-test="closeModal"]').isVisible()).toBe(true);

		wrapper.destroy();
	});

	it("User is redirected to ageError page if max attempts are exceeded by introducing an invalid birth date", async () => {
		brand.state.config.max_attempts_child = 1;
		mockRoute = {
			name: "Error",
			params: {
				error: "ageError",
				route: "Status",
			},
		};

		wrapper = await mount(ChildForm, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		await wrapper.find('[data-test="child-name-input"]').setValue("Name");
		await wrapper.find('[data-test="child-surname-input"]').setValue("Surname");
		await wrapper.find('[data-test="child-nationality-input"]').setValue("Sp");
		await wrapper
			.find('[data-test="child-nationality-input-option"]')
			.trigger("click");
		await wrapper.find("#child-birthday-input").setValue(getBirthDate(15));

		await wrapper.vm.validateChildData();
		await wrapper.vm.validateChildData();

		expect(wrapper.vm.$router.push).toHaveBeenCalledWith({
			name: "Error",
			params: {
				error: "ageError",
				route: "Status",
			},
		});

		wrapper.destroy();
	});

	it("isChild method returns correct value", async () => {
		let checkInDate = "2022-07-15";
		let maxAgeToNotRequireID = 16;
		const childRequiresID = childsManagement.methods.childRequiresDocument;
		jest.spyOn(childsManagement.methods, "childRequiresDocument");

		expect(
			childRequiresID("2022-07-14", checkInDate, maxAgeToNotRequireID),
		).toBeFalsy();

		expect(
			childRequiresID("2005-07-16", checkInDate, maxAgeToNotRequireID),
		).toBeFalsy();

		expect(
			childRequiresID("2005-07-15", checkInDate, maxAgeToNotRequireID),
		).toBeTruthy();

		expect(
			childRequiresID("2005-07-14", checkInDate, maxAgeToNotRequireID),
		).toBeTruthy();

		expect(
			childRequiresID("2002-03-15", checkInDate, maxAgeToNotRequireID),
		).toBeTruthy();

		checkInDate = "2025-07-15";
		maxAgeToNotRequireID = 12;

		expect(
			childRequiresID("2025-07-14", checkInDate, maxAgeToNotRequireID),
		).toBeFalsy();

		expect(
			childRequiresID("2012-07-16", checkInDate, maxAgeToNotRequireID),
		).toBeFalsy();

		expect(
			childRequiresID("2012-07-15", checkInDate, maxAgeToNotRequireID),
		).toBeTruthy();

		expect(
			childRequiresID("2012-07-14", checkInDate, maxAgeToNotRequireID),
		).toBeTruthy();

		expect(
			childRequiresID("2002-03-15", checkInDate, maxAgeToNotRequireID),
		).toBeTruthy();
	});

	it("should show error modal if older than default US adult age(21)", async () => {
		brand.state.config = {
			identification: {
				child_form: [
					[
						{
							active: "true",
							required: "false",
							maxLength: "50",
							minLength: "2",
							name: "name",
							type: "text",
						},
						{
							active: "true",
							required: "false",
							maxLength: "50",
							minLength: "2",
							name: "surname",
							type: "text",
						},
						{
							active: "true",
							required: "false",
							name: "nationality",
						 type: "autocomplete",
							options: countries,
						},
						{
							active: "true",
							required: "true",
							name: "birthday",
							type: "date",
						},
					],
				],
			},
			child_required_identity_documents_age: 21,
			max_attempts_child: 20,
		};

		brand.state.country = "US";
		reservations.state.reservationSelected.check_in = "2024-02-21";
		wrapper = await mount(ChildForm, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
		});
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		const currentDate = new Date();
		const childYearsOld = 22;
		currentDate.setFullYear(currentDate.getFullYear() - childYearsOld - 1);
		const formattedDate = `${currentDate
			.getDate()
			.toString()
			.padStart(2, "0")}-${(currentDate.getMonth() + 1)
			.toString()
			.padStart(2, "0")}-${currentDate.getFullYear()}`;

		await wrapper.find("#child-birthday-input").setValue(formattedDate);
		await wrapper.vm.validateChildData();
		await nextTick();
		expect(storeDispatch).toBeCalledWith("modal/VISIBLE", true);
		expect(wrapper.vm.$store.state.modal.name).toBe("childFormError");
		expect(wrapper.vm.$store.state.modal.show).toBe(true);
	
		const modalsError = wrapper.findAll('[data-test="modalError"]');
		const secondModal = modalsError.at(1);
		expect(secondModal.isVisible()).toBe(true);
		wrapper.destroy();
	});

	it("should not show error modal if child is younget than 21 on US country default adult age", async () => {
		modal.state.show = false;
		modal.state.name = "default";
		brand.state.config = {
			identification: {
				child_form: [
					[
						{
							active: "true",
							required: "false",
							maxLength: "50",
							minLength: "2",
							name: "name",
							type: "text",
						},
						{
							active: "true",
							required: "false",
							maxLength: "50",
							minLength: "2",
							name: "surname",
							type: "text",
						},
						{
							active: "true",
							required: "false",
							name: "nationality",
						 type: "autocomplete",
							options: countries,
						},
						{
							active: "true",
							required: "true",
							name: "birthday",
							type: "date",
						},
						{
							active: "true",
							fill_from_holder: "true",
							required: "false",
							name: "telephone",
							type: "phone",
						},
						{
							active: "true",
							fill_from_holder: "true",
							required: "false",
							name: "email",
							type: "email",
						},
					],
				],
			},
			child_required_identity_documents_age: 21,
			max_attempts_child: 20,
		};

		brand.state.country = "US";
		reservations.state.reservationSelected.check_in = "2022-02-18";
		wrapper = await mount(ChildForm, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
		});
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		const currentDate = new Date();
		const childYearsOld = 20;
		currentDate.setFullYear(currentDate.getFullYear() - childYearsOld);

		const formattedDate = `${currentDate
			.getDate()
			.toString()
			.padStart(2, "0")}-${(currentDate.getMonth() + 1)
			.toString()
			.padStart(2, "0")}-${currentDate.getFullYear()}`;
		await wrapper.find("#child-birthday-input").setValue(formattedDate);
		await nextTick();
		await wrapper.vm.validateChildData();
		expect(storeDispatch).toBeCalledWith("guest/UPDATE_GUEST", {
			pax_type: "CH",
			birthday_date: getBirthDateFormatted(childYearsOld),
			address: {
				CCAA: "",
				city: "",
				country: "",
				postal_code: "",
				province: "",
				region: "",
				street: "",
				street_number: "",
				subregion: "",
			},
			email: "",
			telephone: {
				value: "",
				dialCode: "+1",
				countryCode: ""
			}
		});
		expect(wrapper.vm.$store.state.modal.name).toBe("default");
		expect(wrapper.vm.$store.state.modal.show).toBe(false);
		const modalsError = wrapper.findAll('[data-test="modalError"]');
		const secondModal = modalsError.at(1);
		expect(secondModal.isVisible()).toBe(false);
		wrapper.destroy();
	});
	it("should show error modal if older than default country adult age(not US)", async () => {
		const originalModalState = {...modal.state}
		const originalBrandState = {...brand.state}
		const originalReservationState = {...reservations.state}
		try{

		
		modal.state.show = false;
		modal.state.name = "default";
		brand.state.config = {
			identification: {
				child_form: [
					[
						{
							active: "true",
							required: "false",
							maxLength: "50",
							minLength: "2",
							name: "name",
							type: "text",
						},
						{
							active: "true",
							required: "false",
							maxLength: "50",
							minLength: "2",
							name: "surname",
							type: "text",
						},
						{
							active: "true",
							required: "false",
							name: "nationality",
						 type: "autocomplete",
							options: countries,
						},
						{
							active: "true",
							required: "true",
							name: "birthday",
							type: "date",
						},
					],
				],
			},
			child_required_identity_documents_age: 20,
			max_attempts_child: 20,
		};

		brand.state.country = "ES";
		reservations.state.reservationSelected.check_in = "2022-07-15";
		reservations.state.childAgeAttempts = 0;


		wrapper = await mount(ChildForm, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
					mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
		});
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		const currentDate = new Date();
		const childYearsOld = 20;
		currentDate.setFullYear(currentDate.getFullYear() - childYearsOld);
		await wrapper.find("#child-birthday-input").setValue('26-07-1998');
		await wrapper.vm.validateChildData();
		await nextTick();
		expect(storeDispatch).toBeCalledWith("modal/VISIBLE", true);
		expect(wrapper.vm.$store.state.modal.name).toBe("childFormError");
		expect(wrapper.vm.$store.state.modal.show).toBe(true);
		const modalsError = wrapper.findAll('[data-test="modalError"]');
		const secondModal = modalsError.at(1);
		expect(secondModal.isVisible()).toBe(true);
		wrapper.destroy();
	}finally{
		modal.state = originalModalState
		brand.state = originalBrandState
		reservations.state = originalReservationState
	}
	});
	it("should not show error modal if child is younger than default country adult age (not US)", async () => {
		modal.state.show = false;
		modal.state.name = "default";
		brand.state.config = {
			identification: {
				child_form: [
					[
						{
							active: "true",
							required: "false",
							maxLength: "50",
							minLength: "2",
							name: "name",
							type: "text",
						},
						{
							active: "true",
							required: "false",
							maxLength: "50",
							minLength: "2",
							name: "surname",
							type: "text",
						},
						{
							active: "true",
							required: "false",
							name: "nationality",
						 type: "autocomplete",
							options: countries,
						},
						{
							active: "true",
							required: "true",
							name: "birthday",
						 type: "date",
						},
					],
				],
			},
			child_required_identity_documents_age: 18,
			max_attempts_child: 3,
		};

		brand.state.country = "ES";
		reservations.state.reservationSelected.check_in = "2022-07-15";
		wrapper = await mount(ChildForm, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
		});
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
		const currentDate = new Date();
		const childYearsOld = 17;
		currentDate.setFullYear(currentDate.getFullYear() - childYearsOld);
		const formattedDate = `${currentDate
			.getDate()
			.toString()
			.padStart(2, "0")}-${(currentDate.getMonth() + 1)
			.toString()
			.padStart(2, "0")}-${currentDate.getFullYear()}`;
		await wrapper.find("#child-birthday-input").setValue(formattedDate);
		await wrapper.vm.validateChildData();
		await nextTick();
		expect(storeDispatch).toBeCalledWith("guest/UPDATE_GUEST", {
			address: {
				CCAA: "",
				city: "",
				country: "",
				postal_code: "",
				province: "",
				region: "",
				street: "",
				street_number: "",
				subregion: "",
			},
			email: "",
			telephone: {
				value: "",
				dialCode: "+1",
				countryCode: ""
			},
			pax_type: "CH",
			birthday_date: getBirthDateFormatted(childYearsOld),
		});
		expect(wrapper.vm.$store.state.modal.name).toBe("default");
		expect(wrapper.vm.$store.state.modal.show).toBe(false);
		const modalsError = wrapper.findAll('[data-test="modalError"]');
		const secondModal = modalsError.at(1);
		expect(secondModal.isVisible()).toBe(false);
		wrapper.destroy();
	});
	it("Fills email, telephone and address fields from holder guest if inputs are empty", async () => {
		guest.getters = {
			...guest.getters,
			getHolderGuest: () => ({
				email: "<EMAIL>",
				telephone: {
					value: "123456789",
					dialCode: "+34",
					countryCode: "ES"
				},
				address: {
					CCAA: "Cataluña",
					city: "Barcelona",
					country: "ESP",
					postal_code: "08001",
					province: "Barcelona",
					region: "Cataluña",
					street: "Calle Falsa",
					street_number: "123",
					subregion: "Barcelonès"
				}
			})
		};
	
		wrapper = await mount(ChildForm, {
			mocks: {
				$route: mockRoute,
				$router: mockRouter,
			},
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});
	
		const storeDispatch = jest.spyOn(wrapper.vm.$store, "dispatch");
	
		await wrapper.find('[data-test="child-name-input"]').setValue("Lucía");
		await wrapper.find('[data-test="child-surname-input"]').setValue("Pérez");
		await wrapper.find('[data-test="child-nationality-input"]').setValue("Sp");
		await wrapper.find('[data-test="child-nationality-input-option"]').trigger("click");
		await wrapper.find("#child-birthday-input").setValue(getBirthDate(10));
	
		await wrapper.vm.validateChildData();
	
		expect(storeDispatch).toBeCalledWith("guest/UPDATE_GUEST", {
			pax_type: "CH",
			birthday_date: getBirthDateFormatted(10),
			name: "Lucía",
			surname: "Pérez",
			nationality: "ESP",
			email: "<EMAIL>",
			telephone: {
				value: "123456789",
				dialCode: "+34",
				countryCode: "ES"
			},
			address: {
				CCAA: "Cataluña",
				city: "Barcelona",
				country: "ESP",
				postal_code: "08001",
				province: "Barcelona",
				region: "Cataluña",
				street: "Calle Falsa",
				street_number: "123",
				subregion: "Barcelonès"
			}
		});
	
		wrapper.destroy();
	});

	it("updates telephone correctly when inputChanged is called", async () => {
		wrapper = await mount(ChildForm, {
			store,
			localVue,
			i18n,
			attachTo: document.body,
		});

		wrapper.vm.inputChanged(
			{ value: "123456789", dialCode: "+34", country: "ES", error: false },
			{ name: "telephone" }
		);

		expect(wrapper.vm.telephone).toEqual({
			value: "123456789",
			dialCode: "+34",
			countryCode: "ES"
		});

		wrapper.vm.inputChanged(
			{ value: "987654321", error: false },
			{ name: "telephone" }
		);

		expect(wrapper.vm.telephone).toEqual({
			value: "987654321",
			dialCode: "+34",
			countryCode: "ES"
		});

		wrapper.destroy();
	});
});
